{"name": "bitespeed2", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "prisma db seed", "db:studio": "prisma studio", "postinstall": "prisma generate", "test": "echo \"Error: no test specified\" && exit 1", "test:identify": "ts-node src/test-identify.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "Node.js Express TypeScript project", "dependencies": {"@prisma/client": "^6.13.0", "@types/express": "^5.0.3", "@types/node": "^24.2.1", "dotenv": "^17.2.1", "express": "^5.1.0", "nodemon": "^3.1.10", "prisma": "^6.13.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "devDependencies": {"rimraf": "^6.0.1"}}