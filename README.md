# Contact Identity Reconciliation API

A Node.js + TypeScript API that reconciles user identities across email and phone numbers. It identifies existing contacts, links related ones, and returns a unified view with a single primary contact and all secondary contacts.

## Tech Stack
- **Node.js**
- **TypeScript**
- **Express**
- **Prisma** (ORM)
- **PostgreSQL**

## Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL instance

### 1) Clone and install
```bash
git clone <your-repo-url>
cd bitespeed2
npm install
```

### 2) Configure environment
Create a `.env` file in the project root:
```bash
# Required by Prisma
DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DBNAME?schema=public"

# Optional
PORT=3000
```

### 3) Generate Prisma client and run migrations
```bash
npm run db:generate
npm run db:migrate
```
If you want a clean slate:
```bash
npm run db:reset
```

### 4) Run the server
- Development (watch mode):
```bash
npm run dev
```
- Production build:
```bash
npm run build
npm start
```

### 5) Docker (optional)
Build and run locally via Docker:
```bash
npm run docker:build
npm run docker:run
```
Or with docker-compose:
```bash
npm run docker:up
# to stop
npm run docker:down
```

## API

### POST /identify
Identifies and consolidates contact information based on email and/or phone number.

- At least one of `email` or `phoneNumber` must be provided.

Request body:
```json
{
  "email": "string (optional)",
  "phoneNumber": "string (optional)"
}
```

Successful response:
```json
{
  "contact": {
    "primaryContatctId": 1,
    "emails": ["<EMAIL>"],
    "phoneNumbers": ["+**********"],
    "secondaryContactIds": [2]
  }
}
```

Notes:
- `primaryContatctId`: ID of the primary contact
- `emails`: Unique emails across the identity (primary first)
- `phoneNumbers`: Unique phone numbers across the identity (primary first)
- `secondaryContactIds`: IDs of all secondary contacts linked to the primary

### Health check
- GET `/health` returns server/database status.

## Hosted API
Placeholder URL: https://your-deployed-endpoint.example.com

Update this section after deployment. You can use `render.yaml` and `DEPLOYMENT.md` for deployment instructions. 