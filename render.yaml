services:
  # PostgreSQL Database Service
  - type: pserv
    name: bitespeed-database
    env: docker
    plan: free
    databases:
      - name: bitespeed
        user: bitespeed_user

  # Web Service
  - type: web
    name: bitespeed-api
    env: docker
    plan: free
    buildCommand: ""
    startCommand: ""
    dockerfilePath: ./Dockerfile
    dockerContext: .
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: DATABASE_URL
        fromDatabase:
          name: bitespeed-database
          property: connectionString
    healthCheckPath: /health
    autoDeploy: true
    buildFilter:
      paths:
        - src/**
        - prisma/**
        - package*.json
        - Dockerfile
        - tsconfig.json
