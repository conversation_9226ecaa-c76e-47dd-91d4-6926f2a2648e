version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: bitespeed-db
    environment:
      POSTGRES_DB: bitespeed
      POSTGRES_USER: bitespeed_user
      POSTGRES_PASSWORD: bitespeed_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U bitespeed_user -d bitespeed"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Web Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bitespeed-api
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: ************************************************************/bitespeed?schema=public
    ports:
      - "3000:3000"
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
